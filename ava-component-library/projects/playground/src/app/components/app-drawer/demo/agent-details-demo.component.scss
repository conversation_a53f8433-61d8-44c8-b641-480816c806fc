/* ===================================================================
   DEMO SECTION STYLES
   =================================================================== */
.demo-section {
  background: var(--color-background-primary, #ffffff);
  padding: 2rem;
  border-radius: var(--global-radius-lg, 0.75rem);
  border: 2px solid var(--color-border-default, #e2e8f0);
  margin-bottom: 2rem;

  h4 {
    margin: 0 0 1rem 0;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--color-text-primary, #1a202c);
  }

  p {
    color: var(--color-text-secondary, #64748b);
    line-height: 1.6;
    margin-top: 0px !important;
    margin-bottom: 0px !important;
  }

  .demo-button {
    background: var(--color-brand-primary, #e91e63);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: var(--global-radius-md, 0.5rem);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: var(--color-brand-primary-hover, #d81b60);
      transform: translateY(-1px);
    }
  }
}

/* ===================================================================
   AGENT DETAILS DRAWER STYLES
   =================================================================== */
.agent-details {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  padding: 0.5rem;
  height: 100%;
}

/* Header Section */
.agent-header {
  .agent-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--color-brand-primary, #e91e63);
    margin: 0 0 1rem 0;
    line-height: 1.2;
  }

  .agent-description {
    font-size: 1rem;
    color: var(--color-text-secondary, #64748b);
    line-height: 1.6;
    margin: 0;
  }
}

/* ===================================================================
   FIGMA DESIGN SPECIFIC STYLES
   =================================================================== */

/* Close Button */
.custom-close-button {
  display: flex;
  width: 36px;
  height: 36px;
  justify-content: center;
  align-items: center;
  aspect-ratio: 1/1;
  position: absolute;
  right: 0;
  background: none;
  border: none;
  cursor: pointer;
  color: #6B7280;
  z-index: 10;

  &:hover {
    color: #374151;
  }
}

/* Content Section */
.drawer-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
  width: 100%;
}

/* Header Section */
.header-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.heading {
  flex: 1 0 0;
  margin: 0;
  /* Heading 2 */
  font-family: var(--Global-Typography-family-Heading, Mulish);
  font-size: var(--Global-Typography-size-xxl, 32px);
  font-style: normal;
  font-weight: var(--Global-Typography-weight-Bold, 700);
  line-height: var(--Global-Typography-line-height-H2, 38.4px); /* 120% */
  background: var(--Gradient-Gradient-Dark, linear-gradient(109deg, var(--Colors-Brand-Primary, #E91E63) 4.7%, var(--Colors-Brand-Secondary, #9C27B0) 94.91%));
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.description {
  align-self: stretch;
  margin: 0;
  color: var(--Brand-and-Accent-Text-Caption, #6B7280);
  /* Heading 4 */
  font-family: var(--Global-Typography-family-Heading, Mulish);
  font-size: var(--Global-Typography-size-lg, 20px);
  font-style: normal;
  font-weight: var(--Global-Typography-weight-Semi-bold, 600);
  line-height: var(--Global-Typography-line-height-H4, 24px); /* 120% */
}

/* Tags Section */
.agent-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin: 24px 0;
}

.agent-tag {
  display: flex;
  width: 145px;
  height: 38px;
  padding: 12px 24px;
  justify-content: space-between;
  align-items: center;
  border-radius: 24px;
  border: 1px solid var(--Gradient-Gradient-Dark, #E91E63);
}

/* Category Section */
.category-section {
  display: flex;
  padding: 12px 24px;
  justify-content: space-between;
  align-items: center;
  align-self: stretch;
  border-radius: 12px;
  border-top: 0.5px solid var(--Gradient-Gradient-Medium, #F06896);
  border-bottom: 0.5px solid var(--Gradient-Gradient-Medium, #F06896);
  flex-direction: column;
  gap: 16px;
  margin: 24px 0;
}

.category-row {
  display: flex;
  justify-content: space-between;
  width: 100%;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  flex: 1;
}

.category-label {
  color: #6B7280;
  font-size: 14px;
  font-weight: 500;
}

.category-type {
  color: #6B7280;
  font-size: 12px;
  font-weight: 400;
}

.category-values {
  display: flex;
  justify-content: space-between;
  width: 100%;
}

.category-value-item {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
}

.nav-arrows {
  display: flex;
  gap: 4px;
}

.nav-arrow {
  background: none;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #6b7280;
  font-size: 14px;

  &:hover {
    background: #f3f4f6;
  }
}

.developer-icon {
  font-size: 16px;
}

.relevancy-score,
.rating-score {
  font-weight: 600;
  color: #1f2937;
}

/* What it's for Section */
.what-its-for-section {
  background: #f8f4ff;
  padding: 20px;
  border-radius: 12px;
  margin: 24px 0;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
}

.section-description {
  margin: 0;
  color: #6b7280;
  line-height: 1.6;
}

/* Footer Section */
.footer-section {
  width: 100%;
}

.footer-button {
  width: 100%;
  height: 52px;
}

/* Drawer Layout - Exact Figma Specifications */
:host ::ng-deep .ava-drawer__content {
  display: flex;
  width: 650px;
  height: 1089px;
  padding: 72px 32px 32px 32px;
  flex-direction: column;
  align-items: center;
  gap: 220px;
  flex-shrink: 0;
  background: var(--Backgorund-Surface, #FFF);
  box-shadow: -2px 2px 4px 0 rgba(0, 0, 0, 0.12);
}

:host ::ng-deep .ava-drawer__body {
  padding: 0 !important;
  background: white !important;
}

/* Remove drawer component borders */
:host ::ng-deep .ava-drawer__header {
  border-bottom: none !important;
  padding: 0px !important;
  display: none !important;
}

:host ::ng-deep .ava-drawer__footer {
  border-top: none !important;
}

/* Stats Grid */
.agent-stats {
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
    padding: 1.5rem 0;
    border-bottom: 1px solid var(--color-border-subtle, #f1f5f9);
  }

  .stat-column {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .stat-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    .stat-label {
      font-size: 0.875rem;
      color: var(--color-text-secondary, #64748b);
      font-weight: 500;
    }

    .nav-arrows {
      display: flex;
      gap: 0.25rem;
      
      .nav-arrow {
        background: none;
        border: none;
        color: var(--color-text-secondary, #64748b);
        cursor: pointer;
        padding: 0.25rem;
        border-radius: var(--global-radius-sm, 0.25rem);
        font-size: 1rem;
        
        &:hover {
          background: var(--color-surface-hover, #f8fafc);
        }
      }
    }

    .stat-icon {
      font-size: 1rem;
      color: var(--color-text-secondary, #64748b);
    }
  }

  .stat-value {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--color-text-primary, #1a202c);
    
    &.highlight {
      color: var(--color-brand-primary, #e91e63);
      font-weight: 700;
    }
  }

  .stat-sublabel {
    font-size: 0.875rem;
    color: var(--color-text-secondary, #64748b);
  }
}

/* Content Section */
.agent-content {
  flex: 1;
  
  .content-section {
    background: var(--color-surface-secondary, #f8fafc);
    padding: 1.5rem;
    border-radius: var(--global-radius-lg, 0.75rem);
    
    .section-title {
      font-size: 1.25rem;
      font-weight: 600;
      color: var(--color-text-primary, #1a202c);
      margin: 0 0 1rem 0;
    }

    .section-text {
      font-size: 0.9375rem;
      color: var(--color-text-secondary, #64748b);
      line-height: 1.6;
      margin: 0;
    }
  }
}

/* Action Buttons */
.agent-actions {
  margin-top: auto;
  padding-top: 1rem;

  .action-button {
    width: 100%;
    background: linear-gradient(135deg, #e91e63 0%, #ad1457 100%);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: var(--global-radius-md, 0.5rem);
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(233, 30, 99, 0.3);
    }
  }
}

.close-actions {
  margin-top: 1rem;

  .close-button {
    background: var(--color-surface-secondary, #f1f5f9);
    color: var(--color-text-secondary, #64748b);
    border: 1px solid var(--color-border-default, #e2e8f0);
    padding: 0.5rem 1rem;
    border-radius: var(--global-radius-sm, 0.25rem);
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: var(--color-surface-hover, #e2e8f0);
    }
  }
}

/* Hidden state */
.hidden {
  display: none;
}

/* ===================================================================
   RESPONSIVE DESIGN
   =================================================================== */
@media (max-width: 768px) {
  .agent-stats .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }
  
  .agent-header .agent-title {
    font-size: 1.75rem;
  }
}

@media (max-width: 480px) {
  .agent-stats .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .agent-details {
    gap: 1.5rem;
  }
}

/* What it's for Section */
.what-its-for-section {
  border: 1px solid var(--color-border-subtle, #e2e8f0);
  border-radius: var(--global-radius-md, 0.375rem);
  padding: 1.5rem;
  margin-bottom: 2rem;

  .section-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--color-text-primary, #1a202c);
    margin: 0 0 1rem 0;
  }

  .section-description {
    font-size: 0.9375rem;
    color: var(--color-text-secondary, #64748b);
    line-height: 1.6;
    margin: 0;
  }
}

/* Footer Section */
.footer-section {
  padding: 1rem 0;
  border-top: 1px solid var(--color-border-subtle, #e2e8f0);
  margin-top: auto;
}
