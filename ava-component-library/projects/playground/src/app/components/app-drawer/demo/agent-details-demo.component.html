<!-- Demo Trigger <PERSON> -->
<div class="demo-section">
  <h4>🎯 Agent Details Demo</h4>
  <p>Click to see the drawer with the exact Figma design implementation (650px × 1089px).</p>

  <ava-button
    label="Open Agent Details"
    (click)="openDrawer()"
    variant="primary">
  </ava-button>

  <!-- Figma Design Drawer -->
  <ava-drawer
    [isOpen]="isDrawerOpen"
    position="right"
    [showHeader]="false"
    [showFooter]="true"
    (closed)="closeDrawer()">

    <!-- Close Button -->
    <button class="custom-close-button" (click)="closeDrawer()">
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    </button>

    <!-- Content Section -->
    <div class="drawer-content">
      <!-- Header Section -->
      <div class="header-section">
        <h1 class="heading">{{ agentData.title }}</h1>
        <p class="description">{{ agentData.subtitle }}</p>
      </div>

      <!-- Tags Section -->
      <div class="agent-tags">
        <ava-tag
          *ngFor="let tag of agentData.tags"
          [label]="tag.label"
          [variant]="tag.variant"
          class="agent-tag">
        </ava-tag>
      </div>

      <!-- Category Section -->
      <div class="category-section">
        <div class="category-row">
          <div class="category-item">
            <span class="category-label">Category</span>
            <span class="category-type">Type</span>
          </div>
          <div class="category-item">
            <span class="category-label">Developed by</span>
            <span class="category-type">Name</span>
          </div>
          <div class="category-item">
            <span class="category-label">Relevancy</span>
            <span class="category-type">Score</span>
          </div>
          <div class="category-item">
            <span class="category-label">Rating</span>
            <span class="category-type">Out of 5</span>
          </div>
        </div>
        <div class="category-values">
          <div class="category-value-item">
            <div class="nav-arrows">
              <button class="nav-arrow">‹</button>
              <button class="nav-arrow">›</button>
            </div>
          </div>
          <div class="category-value-item">
            <div class="developer-icon">👤</div>
          </div>
          <div class="category-value-item">
            <span class="relevancy-score">{{ agentData.stats.relevancy }}</span>
          </div>
          <div class="category-value-item">
            <span class="rating-score">{{ agentData.stats.rating }} ⭐</span>
          </div>
        </div>
      </div>

      <!-- What it's for Section -->
      <div class="what-its-for-section">
        <h3 class="section-title">What it's for</h3>
        <p class="section-description">{{ agentData.whatItsFor }}</p>
      </div>
    </div>

    <!-- Footer Button -->
    <div slot="footer" class="footer-section">
      <ava-button
        label="Label"
        variant="primary"
        class="footer-button">
      </ava-button>
    </div>

  </ava-drawer>
</div>
